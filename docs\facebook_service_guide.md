# Facebook Service Guide

## Overview

The Facebook Service provides a comprehensive interface to Facebook's Graph API, allowing you to post text, images, and videos to your Facebook account programmatically.

## Features

- ✅ **Text Posts**: Post text messages to your Facebook feed
- ✅ **Image Posts**: Post images with optional captions
- ✅ **Video Posts**: Post videos with titles and descriptions
- ✅ **Universal Post Method**: Smart posting that handles text, images, and videos
- ✅ **User Information**: Get authenticated user details
- ✅ **Post Management**: Retrieve and delete posts
- ✅ **Health Checks**: Verify service connectivity
- ✅ **Error Handling**: Comprehensive error handling and logging

## Setup

### 1. Get Facebook Access Token

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or use an existing one
3. Generate a User Access Token with the following permissions:
   - `pages_manage_posts` (for posting to pages)
   - `publish_to_groups` (for posting to groups)
   - `user_posts` (for posting to user timeline)

### 2. Configure Environment

Add your Facebook token to your `.env` file:

```env
FACEBOOK_TOKEN=your_facebook_access_token_here
```

## API Usage

### Basic Text Post

```bash
curl -X POST "http://localhost:8000/facebook.post_text" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, Facebook! 👋"
  }'
```

### Image Post

```bash
curl -X POST "http://localhost:8000/facebook.post_image" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/image.jpg",
    "message": "Check out this beautiful sunset! 🌅"
  }'
```

### Video Post

```bash
curl -X POST "http://localhost:8000/facebook.post_video" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://example.com/video.mp4",
    "title": "My Amazing Video",
    "description": "This is a description of my video content"
  }'
```

### Universal Post Method

```bash
curl -X POST "http://localhost:8000/facebook.post" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "your_facebook_token",
    "text": "Check out my content!",
    "images": "https://example.com/image.jpg"
  }'
```

## Available Methods

| Method | Description | Parameters |
|--------|-------------|------------|
| `post` | Universal posting method | `token`, `text`, `images`, `videos` |
| `post_text` | Post text message | `message`, `token` |
| `post_image` | Post image with caption | `image_url`, `message`, `token` |
| `post_video` | Post video with title/description | `video_url`, `title`, `description`, `token` |
| `get_user_info` | Get user information | `token`, `fields` |
| `get_posts` | Get user's recent posts | `limit`, `token` |
| `delete_post` | Delete a specific post | `post_id`, `token` |
| `health_check` | Check service health | `token` |
| `get_service_info` | Get service information | None |

## Main Method Signature

The primary `post` method follows this signature:

```python
facebook.post(token, text, images, videos)
```

### Parameters:
- `token` (optional): Facebook access token
- `text` (optional): Text message to post
- `images` (optional): URL of image to post
- `videos` (optional): URL of video to post

### Examples:
```python
# Text only
facebook.post(text="Hello Facebook!")

# Text with image
facebook.post(text="Check this out!", images="https://example.com/image.jpg")

# Text with video
facebook.post(text="My video", videos="https://example.com/video.mp4")

# With custom token
facebook.post(token="your_token", text="Hello!", images="https://example.com/image.jpg")
```

## Response Format

All methods return a standardized response format:

```json
{
  "status": "success|error",
  "message": "Human readable message",
  "post_id": "123456789_987654321",
  "id": "123456789_987654321",
  "response": { /* Original Facebook API response */ }
}
```

## Error Handling

The service provides comprehensive error handling:

- **Validation Errors**: Missing required parameters
- **Authentication Errors**: Invalid or expired tokens
- **API Errors**: Facebook API-specific errors
- **Network Errors**: Connection and timeout issues

## Facebook Graph API Endpoints

The service uses the following Facebook Graph API endpoints:

- **Text Posts**: `POST /me/feed`
- **Image Posts**: `POST /me/photos`
- **Video Posts**: `POST /me/videos` (via graph-video.facebook.com)
- **User Info**: `GET /me`
- **User Posts**: `GET /me/posts`
- **Delete Post**: `DELETE /{post_id}`

## Security Notes

- Never expose your Facebook access token in client-side code
- Use environment variables to store sensitive tokens
- Regularly rotate your access tokens
- Monitor your app's usage in Facebook Developer Console

## Troubleshooting

### Common Issues

1. **"Invalid OAuth access token"**
   - Check if your token is valid and not expired
   - Verify the token has the required permissions

2. **"Application request limit reached"**
   - You've hit Facebook's rate limits
   - Wait before making more requests

3. **"Unsupported post request"**
   - Check if the content type and format are correct
   - Ensure URLs are publicly accessible

### Getting Help

- Check Facebook's [Graph API Documentation](https://developers.facebook.com/docs/graph-api/)
- Use the `health_check` method to verify connectivity
- Enable debug logging for detailed error information
