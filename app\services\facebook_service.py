"""
Facebook Service for Common Class API

A comprehensive service for interacting with Facebook's Graph API including:
- Text posts to user's feed
- Image posts with optional messages
- Video posts with optional messages
- Combined media posting

Author: Common Class API
Version: 1.0.0
"""

import logging
from typing import Dict, Any, Optional
import httpx
from app.core.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Facebook:
    """
    Facebook Graph API service for posting content to user's account.

    This service provides methods to post text, images, and videos to a Facebook user's
    account using the Facebook Graph API.
    """

    def __init__(self, **kwargs):
        """
        Initialize Facebook service with authentication token.

        Args:
            token: Facebook access token (optional, will use from config if not provided)
            **kwargs: Additional parameters
        """
        settings = get_settings()
        self.default_token = kwargs.get("token") or kwargs.get("oauth_token") or settings.facebook_token
        self.base_url = "https://graph.facebook.com/v18.0"
        self.video_base_url = "https://graph-video.facebook.com/v18.0"
        self.timeout = 30

        if not self.default_token:
            logger.warning("Facebook token not provided. Some operations may fail.")

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Facebook API requests."""
        return {
            "Content-Type": "application/json"
        }

    def _get_auth_params(self, token: Optional[str] = None) -> Dict[str, str]:
        """Get authentication parameters for Facebook API requests."""
        access_token = token or self.default_token
        return {
            "access_token": access_token
        }

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        token: Optional[str] = None,
        files: Optional[Dict] = None,
        use_video_url: bool = False
    ) -> Dict[str, Any]:
        """
        Make HTTP request to Facebook Graph API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request data
            token: Facebook access token
            files: Files to upload
            use_video_url: Whether to use video upload URL

        Returns:
            API response as dictionary
        """
        base_url = self.video_base_url if use_video_url else self.base_url
        url = f"{base_url}/{endpoint}"

        # Prepare authentication
        auth_params = self._get_auth_params(token)

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "GET":
                    params = {**(data or {}), **auth_params}
                    response = await client.get(url, params=params)
                else:
                    if files:
                        # For file uploads, use form data
                        form_data = {**(data or {}), **auth_params}
                        response = await client.request(
                            method.upper(),
                            url,
                            data=form_data,
                            files=files
                        )
                    else:
                        # For regular posts, use form data (Facebook expects form-encoded data)
                        form_data = {**(data or {}), **auth_params}
                        response = await client.request(
                            method.upper(),
                            url,
                            data=form_data
                        )

                response.raise_for_status()

                if response.status_code == 200 and response.text:
                    result = response.json()
                    return {
                        "status": "success",
                        "response": result,
                        **result
                    }
                else:
                    return {
                        "status": "success",
                        "status_code": response.status_code,
                        "message": "Request completed successfully"
                    }

        except httpx.HTTPStatusError as e:
            error_detail = "Unknown error"
            try:
                error_response = e.response.json()
                if "error" in error_response:
                    error_detail = error_response["error"].get("message", str(e))
                else:
                    error_detail = str(error_response)
            except:
                error_detail = str(e)

            logger.error(f"Facebook API HTTP error: {error_detail}")
            return {
                "status": "error",
                "error": error_detail,
                "status_code": e.response.status_code,
                "message": f"Facebook API request failed: {error_detail}"
            }

        except Exception as e:
            logger.error(f"Facebook API request error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": f"Failed to make Facebook API request: {str(e)}"
            }

    async def post_text(self, message: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Post a text message to user's Facebook feed.

        Args:
            message: Text message to post
            token: Facebook access token (optional)
            **kwargs: Additional parameters (link, privacy, etc.)

        Returns:
            API response with post ID and status

        Example:
            await facebook.post_text("Hello, Facebook!")
            await facebook.post_text("Check this out!", link="https://example.com")
        """
        if not message:
            return {
                "status": "error",
                "error": "Message is required for text posts"
            }

        data = {
            "message": message,
            **kwargs
        }

        result = await self._make_request("POST", "me/feed", data, token)

        if result.get("status") == "success" and "id" in result:
            result["message"] = f"Text post created successfully with ID: {result['id']}"
            result["post_id"] = result["id"]

        return result

    async def post_image(self, image_url: str, message: Optional[str] = None, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Post an image to user's Facebook account.

        Args:
            image_url: URL of the image to post
            message: Optional caption/message for the image
            token: Facebook access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response with post ID and status

        Example:
            await facebook.post_image("https://example.com/image.jpg")
            await facebook.post_image("https://example.com/image.jpg", "Beautiful sunset!")
        """
        if not image_url:
            return {
                "status": "error",
                "error": "Image URL is required"
            }

        data = {
            "url": image_url,
            **kwargs
        }

        if message:
            data["message"] = message

        result = await self._make_request("POST", "me/photos", data, token)

        if result.get("status") == "success" and "id" in result:
            result["message"] = f"Image post created successfully with ID: {result['id']}"
            result["post_id"] = result["id"]

        return result

    async def post_video(self, video_url: str, title: Optional[str] = None, description: Optional[str] = None, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Post a video to user's Facebook account.

        Args:
            video_url: URL of the video to post
            title: Optional title for the video
            description: Optional description for the video
            token: Facebook access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response with post ID and status

        Example:
            await facebook.post_video("https://example.com/video.mp4")
            await facebook.post_video("https://example.com/video.mp4", "My Video", "Check out this video!")
        """
        if not video_url:
            return {
                "status": "error",
                "error": "Video URL is required"
            }

        data = {
            "file_url": video_url,
            **kwargs
        }

        if title:
            data["title"] = title
        if description:
            data["description"] = description

        result = await self._make_request("POST", "me/videos", data, token, use_video_url=True)

        if result.get("status") == "success" and "id" in result:
            result["message"] = f"Video post created successfully with ID: {result['id']}"
            result["post_id"] = result["id"]

        return result

    async def post(self, token: Optional[str] = None, text: Optional[str] = None, images: Optional[str] = None, videos: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Universal post method that can handle text, images, and videos.

        Args:
            token: Facebook access token (optional)
            text: Text message to post (optional)
            images: URL of image to post (optional)
            videos: URL of video to post (optional)
            **kwargs: Additional parameters

        Returns:
            API response with post details

        Example:
            await facebook.post(token="your_token", text="Hello Facebook!")
            await facebook.post(token="your_token", text="Check this out!", images="https://example.com/image.jpg")
            await facebook.post(token="your_token", text="My video", videos="https://example.com/video.mp4")
        """
        if not text and not images and not videos:
            return {
                "status": "error",
                "error": "At least one of text, images, or videos is required"
            }

        # If video is provided, prioritize video posting
        if videos:
            title = kwargs.pop("title", None)
            description = kwargs.pop("description", text)
            return await self.post_video(videos, title, description, token, **kwargs)

        # If image is provided, post image with optional message
        elif images:
            return await self.post_image(images, text, token, **kwargs)

        # Otherwise, post text message
        else:
            return await self.post_text(text, token, **kwargs)

    async def get_user_info(self, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get basic information about the authenticated user.

        Args:
            token: Facebook access token (optional)
            **kwargs: Additional parameters

        Returns:
            User information

        Example:
            await facebook.get_user_info()
        """
        fields = kwargs.get("fields", "id,name,email")
        data = {"fields": fields}

        result = await self._make_request("GET", "me", data, token)

        if result.get("status") == "success":
            result["message"] = "User information retrieved successfully"

        return result

    async def get_posts(self, limit: int = 10, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get user's recent posts.

        Args:
            limit: Number of posts to retrieve (default: 10)
            token: Facebook access token (optional)
            **kwargs: Additional parameters

        Returns:
            List of user's posts

        Example:
            await facebook.get_posts(limit=5)
        """
        data = {
            "limit": limit,
            **kwargs
        }

        result = await self._make_request("GET", "me/posts", data, token)

        if result.get("status") == "success":
            result["message"] = f"Retrieved {len(result.get('data', []))} posts"

        return result

    async def delete_post(self, post_id: str, token: Optional[str] = None) -> Dict[str, Any]:
        """
        Delete a specific post.

        Args:
            post_id: ID of the post to delete
            token: Facebook access token (optional)

        Returns:
            Deletion status

        Example:
            await facebook.delete_post("123456789_987654321")
        """
        if not post_id:
            return {
                "status": "error",
                "error": "Post ID is required"
            }

        result = await self._make_request("DELETE", post_id, None, token)

        if result.get("status") == "success":
            result["message"] = f"Post {post_id} deleted successfully"

        return result

    async def health_check(self, token: Optional[str] = None) -> Dict[str, Any]:
        """
        Check if the Facebook service is working properly.

        Args:
            token: Facebook access token (optional)

        Returns:
            Health check status

        Example:
            await facebook.health_check()
        """
        try:
            # Try to get user info as a health check
            user_info = await self.get_user_info(token=token, fields="id,name")

            if user_info["status"] == "success":
                return {
                    "status": "success",
                    "message": "Facebook service is healthy",
                    "api_accessible": True,
                    "user_id": user_info.get("id"),
                    "user_name": user_info.get("name")
                }
            else:
                return {
                    "status": "error",
                    "message": "Facebook service health check failed",
                    "api_accessible": False,
                    "error": user_info.get("error", "Unknown error")
                }

        except Exception as e:
            return {
                "status": "error",
                "message": "Facebook service health check failed",
                "api_accessible": False,
                "error": str(e)
            }

    async def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the Facebook service.

        Returns:
            Service information and available methods

        Example:
            await facebook.get_service_info()
        """
        return {
            "service": "Facebook Graph API",
            "version": "1.0.0",
            "description": "Facebook service for posting text, images, and videos",
            "base_url": self.base_url,
            "video_base_url": self.video_base_url,
            "timeout": self.timeout,
            "has_token": bool(self.default_token),
            "available_methods": [
                "post", "post_text", "post_image", "post_video",
                "get_user_info", "get_posts", "delete_post",
                "health_check", "get_service_info"
            ],
            "main_method_signature": "post(token, text, images, videos)",
            "parameter_info": {
                "token": "Facebook access token (optional)",
                "text": "Text message to post (optional)",
                "images": "URL of image to post (optional)",
                "videos": "URL of video to post (optional)"
            },
            "endpoints": {
                "text_posts": "me/feed",
                "image_posts": "me/photos",
                "video_posts": "me/videos",
                "user_info": "me",
                "user_posts": "me/posts"
            }
        }